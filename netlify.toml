# Netlify configuration for Next.js 15 with App Router
# Following <PERSON>lify's latest recommendations for Next.js projects

[build]
  command = "npm run build"
  publish = ".next"

[build.environment]
  # Use Node.js 18 LTS for compatibility
  NODE_VERSION = "18"
  # Disable Next.js telemetry for faster builds
  NEXT_TELEMETRY_DISABLED = "1"

# Security and performance headers
[[headers]]
  for = "/*"
  [headers.values]
    # Security headers
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    X-XSS-Protection = "1; mode=block"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=()"

# Cache static assets for optimal performance
[[headers]]
  for = "/_next/static/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# Cache images for 1 week
[[headers]]
  for = "/images/*"
  [headers.values]
    Cache-Control = "public, max-age=604800"

# API routes - no caching for dynamic content
[[headers]]
  for = "/api/*"
  [headers.values]
    Cache-Control = "no-cache, no-store, must-revalidate"

# Handle client-side routing - this is CRITICAL for SPA routing
[[redirects]]
  from = "/*"
  to = "/"
  status = 200
  force = false

# Environment variables that need to be set in Netlify UI:
#
# Supabase Configuration:
# NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
# NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
# SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
#
# Twilio WhatsApp Integration:
# TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
# TWILIO_AUTH_TOKEN=your-auth-token
# TWILIO_WHATSAPP_NUMBER=whatsapp:+***********
#
# AI and Analytics:
# OPENROUTER_API_KEY=your-openrouter-key
#
# Payment Processing (IntaSend):
# INTASEND_PUBLISHABLE_KEY=your-publishable-key
# INTASEND_SECRET_KEY=your-secret-key
